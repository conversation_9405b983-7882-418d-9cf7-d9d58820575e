import React from 'react';
import VoiceDialog from '@/features/voiceai/ui/VoiceDialog';
import { useVoiceDialog } from '@/contexts/VoiceDialogContext';

import  RenderMode  from '@/features/voiceai/ui/VoiceDialog';

interface GlobalVoiceDialogProps {
  renderMode: RenderMode;
}

const GlobalVoiceDialog: React.FC = () => {
  const { isVoiceDialogOpen, closeVoiceDialog, businessId } = useVoiceDialog();

  return (
    <VoiceDialog
      isOpen={isVoiceDialogOpen}
      onClose={closeVoiceDialog}
      renderMode="navigation"
      businessId={businessId}
    />
  );
};

export default GlobalVoiceDialog;
