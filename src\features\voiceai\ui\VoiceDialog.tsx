import { motion, AnimatePresence } from "framer-motion";
import {
  X,
  PhoneCall,
  PhoneOutgoing,
  Phone,
  Search,
  Zap,
  Clock,
} from "lucide-react";

import { useAuth } from "@/hooks/auth/useAuth";
import { useBusinessAgent } from "@/hooks/useBusinessAgent";

import { useState, useEffect, useRef, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import { useAssistantDetails } from "../../../hooks/useAssistantDetails";
import { useVoiceConversation } from "../hooks/useVoiceConversation";

import { supabase } from "@/integrations/supabase/client";
import type { Database } from "@/integrations/supabase/types";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

import { QuickDealCard } from "../../../components/deals/QuickDealCard";
import { QuickBookingCard } from "../../../components/bookings/QuickBookingCard";
import type { BookingWithDetails } from "@/types/booking";
import {
  DEFAULT_ASSISTANT_IMAGE,
  DEFAULT_ASSISTANT_NAME,
} from "@/features/voiceai/data/assistant";

// Configuration constant to control dialog mode

// Add Deal type
type Deal = Database["public"]["Tables"]["deals"]["Row"] & {
  businesses?: {
    name: string;
    address: string;
    city: string;
    zip_code: string;
    state: string;
    country: string;
  };
};

// Add Booking type
type Booking = Database["public"]["Tables"]["bookings"]["Row"] & {
  deals?: {
    title: string;
    images: string[] | null;
    discounted_price: number;
    businesses?: {
      name: string;
      address: string;
      city: string;
      zip_code: string;
      state: string;
      country: string;
    } | null;
  } | null;
};

interface VoiceDialogProps {
  isOpen: boolean;
  onClose: () => void;
  renderMode: RenderMode;
  businessId?: string;
}

type RenderMode = "conversation" | "navigation";

interface NavigationCommand {
  type: "navigate";
  route: string;
  params?: Record<string, any>;
}

const VoiceDialog = ({
  isOpen,
  onClose,
  renderMode,
  businessId,
}: VoiceDialogProps) => {
  const { user, userDetails, getFullName } = useAuth();
  const [showLoginAlert, setShowLoginAlert] = useState(false);
  const [showBalloon, setShowBalloon] = useState(false);
  const [balloonXPosition, setBalloonXPosition] = useState("-50%");
  const [balloonMessage, setBalloonMessage] = useState("");
  const [balloonType, setBalloonType] = useState<"info" | "success" | "error">(
    "info"
  );

  // Voice mode state
  //const [renderModeState, setRenderModeState] = useState<RenderMode>(renderMode);
  const [pendingNavigation, setPendingNavigation] =
    useState<NavigationCommand | null>(null);
  const navigate = useNavigate();

  const [deals, setDeals] = useState<Deal[]>([]);
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [loadingDeal, setLoadingDeal] = useState(false);
  const [loadingBooking, setLoadingBooking] = useState(false);
  const [currentNotificationType, setCurrentNotificationType] = useState<
    string | null
  >(null);

  // Fetch business agent details when businessId is provided
  const { agent: businessAgent, isLoading: isLoadingAgent } = useBusinessAgent(
    businessId,
    isOpen && !!businessId
  );

  // Use business agent details if available, otherwise fall back to user's selected assistant
  const assistantDetails = useAssistantDetails(
    isOpen,
    businessAgent ? undefined : userDetails?.selected_assistant_id
  );

  // Prepare agent details for voice conversation
  // Use business agent data if available, otherwise use assistant details
  const agentData = businessAgent
    ? {
        id: businessAgent.id,
        name: businessAgent.name,
        description: businessAgent.description,
        image_url: businessAgent.avatar_url,
        voice_id: businessAgent.voice_id,
        instructions: businessAgent.instructions,
        personality_style: businessAgent.personality_style,
        voice_settings: businessAgent.voice_settings,
      }
    : assistantDetails;

  const {
    transcript,
    error,

    conversation,
    startConversation,
    endConversation,

    notifications,
    removeNotification,
    clearNotifications,
    isSearching,
    searchQuery,
    startBusinessConversation,
  } = useVoiceConversation(userDetails?.first_name, agentData);

  const [isStarting, setIsStarting] = useState(false);

  const [currentAiMessage, setCurrentAiMessage] = useState("");

  // Connection quality states
  const [connectionQuality, setConnectionQuality] = useState<
    "excellent" | "good" | "unstable"
  >("excellent");
  const [audioLevel, setAudioLevel] = useState(0.5); // Simulated audio level for visualizer

  // Timer states
  const [conversationDuration, setConversationDuration] = useState(0);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const startTimeRef = useRef<number | null>(null);

  // Format time function
  const formatTime = useCallback((seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;

    if (hours > 0) {
      return `${hours.toString().padStart(2, "0")}:${minutes
        .toString()
        .padStart(2, "0")}:${remainingSeconds.toString().padStart(2, "0")}`;
    }
    return `${minutes.toString().padStart(2, "0")}:${remainingSeconds
      .toString()
      .padStart(2, "0")}`;
  }, []);

  // Timer management
  const startTimer = useCallback(() => {
    if (timerRef.current) return; // Already running

    startTimeRef.current = Date.now();
    timerRef.current = setInterval(() => {
      if (startTimeRef.current) {
        const elapsed = Math.floor((Date.now() - startTimeRef.current) / 1000);
        setConversationDuration(elapsed);
      }
    }, 1000);
  }, []);

  const stopTimer = useCallback(() => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
    startTimeRef.current = null;
    setConversationDuration(0);
  }, []);

  // Navigation detection and handling
  const detectNavigationCommand = useCallback(
    (message: string): NavigationCommand | null => {
      const lowerMessage = message.toLowerCase();

      // Define navigation patterns and their corresponding routes
      const navigationPatterns = [
        {
          patterns: [
            "vai alla home",
            "torna alla home",
            "homepage",
            "pagina principale",
          ],
          route: "/",
        },
        {
          patterns: ["vai alle offerte", "mostra offerte", "deals", "sconti"],
          route: "/deals",
        },
        {
          patterns: [
            "vai alle prenotazioni",
            "le mie prenotazioni",
            "bookings",
          ],
          route: "/le-mie-prenotazioni",
        },
        {
          patterns: ["vai al profilo", "profilo utente", "account"],
          route: "/profile",
        },
        {
          patterns: ["vai alle esperienze", "esperienze", "experiences"],
          route: "/experiences",
        },
        {
          patterns: ["vai al social", "social", "community"],
          route: "/social",
        },
        { patterns: ["vai alla mappa", "mappa", "map"], route: "/map" },
        { patterns: ["vai al meteo", "meteo", "weather"], route: "/meteo" },
      ];

      for (const { patterns, route } of navigationPatterns) {
        if (patterns.some((pattern) => lowerMessage.includes(pattern))) {
          return { type: "navigate", route };
        }
      }

      return null;
    },
    []
  );

  const handleNavigationCommand = useCallback(
    (command: NavigationCommand) => {
      // Switch to navigation mode
      //setRenderModeState("navigation");
      setPendingNavigation(command);

      // Execute navigation after a short delay to allow UI transition
      setTimeout(() => {
        navigate(command.route);
        setPendingNavigation(null);
      }, 500);
    },
    [navigate]
  );

  // Function to fetch deal by ID
  const fetchDealById = async (dealId: string) => {
    setLoadingDeal(true);
    try {
      const { data, error } = await supabase
        .from("deals")
        .select(
          `
          *,
          businesses (
            name,
            address,
            city,
            zip_code,
            state,
            country
          )
        `
        )
        .eq("id", dealId)
        .single();

      if (error) {
        console.error("Error fetching deal:", error);
        showBalloonNotification(
          "❌ Errore nel caricamento dell'offerta",
          "error"
        );
        return null;
      }

      return data as Deal;
    } catch (error) {
      console.error("Error fetching deal:", error);
      showBalloonNotification(
        "❌ Errore nel caricamento dell'offerta",
        "error"
      );
      return null;
    } finally {
      setLoadingDeal(false);
    }
  };

  // Function to fetch booking by ID
  const fetchBookingById = async (bookingId: string) => {
    setLoadingBooking(true);
    try {
      const { data, error } = await supabase
        .from("bookings")
        .select(
          `
          *,
          deals (
            title,
            images,
            discounted_price,
            businesses (
              name,
              address,
              city,
              zip_code,
              state,
              country
            )
          )
        `
        )
        .eq("id", bookingId)
        .single();

      if (error) {
        console.error("Error fetching booking:", error);
        showBalloonNotification(
          "❌ Errore nel caricamento della prenotazione",
          "error"
        );
        return null;
      }

      return data as Booking;
    } catch (error) {
      console.error("Error fetching booking:", error);
      showBalloonNotification(
        "❌ Errore nel caricamento della prenotazione",
        "error"
      );
      return null;
    } finally {
      setLoadingBooking(false);
    }
  };

  // Balloon messages array
  const balloonMessages = [
    "💬 Sto ascoltando...",
    "🎯 Come posso aiutarti?",
    "✨ Parla pure!",
    "🔊 Ti sento perfettamente",
    "🤖 Sono qui per te",
    "💡 Fammi una domanda",
  ];

  // Function to show balloon notification
  const showBalloonNotification = (
    message: string,
    type: "info" | "success" | "error" = "info"
  ) => {
    setBalloonMessage(message);
    setBalloonType(type);
    const randomX = Math.random() * 80 + 10; // 10% to 90%
    setBalloonXPosition(`${randomX}%`);
    setShowBalloon(true);

    // Auto hide after 4 seconds
    setTimeout(() => {
      setShowBalloon(false);
    }, 4000);
  };

  const handleClose = () => {
    // End any active conversation
    endConversation();
    // Stop timer
    stopTimer();
    // Clear notifications, deals, and bookings
    clearNotifications();
    setDeals([]);
    setBookings([]);

    onClose();
  };

  const handleMicClick = () => {
    if (!user) {
      setShowLoginAlert(true);
      return;
    }

    if (conversation.status === "connected") {
      // When hanging up, end conversation and reset notifications
      endConversation();
      stopTimer();
      clearNotifications();
      setDeals([]);
      setBookings([]);
    } else {
      setIsStarting(true);
      if (businessId) {
        startBusinessConversation(businessId);
      } else {
        startConversation();
      }
    }
  };

  // Timer lifecycle management
  useEffect(() => {
    if (conversation.status === "connected") {
      startTimer();
    } else {
      stopTimer();
    }
  }, [conversation.status, startTimer, stopTimer]);

  // Cleanup timer on unmount
  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, []);

  // Balloon timer effect for random messages
  // useEffect(() => {
  //   let balloonTimer: NodeJS.Timeout;

  //   if (isOpen && conversation.status === "connected" && !balloonMessage) {
  //     balloonTimer = setInterval(() => {
  //       // Only show random balloons if no custom message is active
  //       if (!balloonMessage) {
  //         const randomX = Math.random() * 80 + 10; // 10% to 90%
  //         setBalloonXPosition(`${randomX}%`);
  //         setShowBalloon(true);

  //         // Auto hide after animation completes (4 seconds total)
  //         setTimeout(() => {
  //           setShowBalloon(false);
  //         }, 4000);
  //       }
  //     }, 60000); // Every minute
  //   }

  //   return () => {
  //     if (balloonTimer) {
  //       clearInterval(balloonTimer);
  //     }
  //   };
  // }, [isOpen, conversation.status, balloonMessage]);

  useEffect(() => {
    if (!isOpen) {
      handleClose();
    }
  }, [isOpen]);

  useEffect(() => {
    if (conversation.status === "connected") {
      setIsStarting(false);
    }

    if (conversation.status === "disconnected") {
      setIsStarting(false);
    }
  }, [conversation.status]);

  // Monitor transcript for navigation commands
  useEffect(() => {
    if (transcript && conversation.status === "connected") {
      const navigationCommand = detectNavigationCommand(transcript);
      if (navigationCommand) {
        console.log("Navigation command detected:", navigationCommand);
        handleNavigationCommand(navigationCommand);
      }
    }
  }, [
    transcript,
    conversation.status,
    detectNavigationCommand,
    handleNavigationCommand,
  ]);

  // Handle notifications
  useEffect(() => {
    if (notifications.length > 0) {
      const latestNotification = notifications[notifications.length - 1];

      // Set current notification type for animation control
      setCurrentNotificationType(latestNotification.metadata);

      if (latestNotification.metadata === "deal") {
        // Fetch and add the deal to the array
        fetchDealById(latestNotification.entityId).then((deal) => {
          if (deal) {
            setDeals((prevDeals) => {
              // Check if deal already exists to avoid duplicates
              const exists = prevDeals.some((d) => d.id === deal.id);
              if (!exists) {
                return [...prevDeals, deal];
              }
              return prevDeals;
            });
            showBalloonNotification(
              `🎯 Deal ricevuto! ${deal.title}`,
              "success"
            );
          }
          // Clear notification type after processing
          setCurrentNotificationType(null);
        });
      }
      if (latestNotification.metadata === "booking") {
        // Fetch and add the booking to the array
        fetchBookingById(latestNotification.entityId).then((booking) => {
          if (booking) {
            setBookings((prevBookings) => {
              // Check if booking already exists to avoid duplicates
              const exists = prevBookings.some((b) => b.id === booking.id);
              if (!exists) {
                return [...prevBookings, booking];
              }
              return prevBookings;
            });
            showBalloonNotification(
              `📅 Prenotazione ricevuta! ${
                booking.deals?.title || "Nuova prenotazione"
              }`,
              "success"
            );
          }
          // Clear notification type after processing
          setCurrentNotificationType(null);
        });
      }
      if (latestNotification.metadata === "message") {
        showBalloonNotification(
          `🧠  ${latestNotification.message}`, // Here we have the message from the LLM. It's a temporary solution to show the message from the LLM.
          "success"
        );
        // Clear notification type after processing
        setCurrentNotificationType(null);
      }
      if (latestNotification.metadata === "query") {
        // Handle query notification - SearchingAnimation will show based on isSearching
        // Clear notification type when search completes
        setTimeout(() => setCurrentNotificationType(null), 3000);
      }
      if (latestNotification.metadata === "action") {
        // Handle action notification - ActionAnimation will show
        // Clear notification type after some time
        setTimeout(() => setCurrentNotificationType(null), 3000);
      }

      // Remove the notification after processing it
      removeNotification(latestNotification.id);
    }
  }, [notifications, removeNotification]);

  // Clear notifications, deals, and bookings when dialog closes
  useEffect(() => {
    if (!isOpen) {
      clearNotifications();
      setDeals([]);
      setBookings([]);
      setCurrentNotificationType(null);
    }
  }, [isOpen, clearNotifications]);

  // Dynamic connection quality monitoring - simulates quality assessment every 5 seconds
  useEffect(() => {
    let qualityTimer: NodeJS.Timeout;

    if (isOpen && conversation.status === "connected") {
      qualityTimer = setInterval(() => {
        // Simulate dynamic quality assessment
        const qualities: Array<"excellent" | "good" | "unstable"> = [
          "excellent",
          "good",
          "unstable",
        ];
        const randomQuality =
          qualities[Math.floor(Math.random() * qualities.length)];
        setConnectionQuality(randomQuality);

        // Simulate audio level changes
        setAudioLevel(Math.random() * 0.8 + 0.2); // Between 0.2 and 1.0
      }, 5000);
    }

    return () => {
      if (qualityTimer) {
        clearInterval(qualityTimer);
      }
    };
  }, [isOpen, conversation.status]);

  // async function handleTestNotification(): Promise<void> {
  //   addNotification("a1a9a924-c650-4fd3-8941-2ae62cbcb852");
  //   await new Promise((resolve) => setTimeout(resolve, 5000));
  //   addNotification("aa0a0885-3ca3-4b79-9311-fedab3dadffa");
  // }

  // AI Activity Messages
  const aiActivityMessages = [
    "🤖 Sto analizzando i dati...",
    "🧠 Elaborando con l'intelligenza artificiale...",
    "⚡ Algoritmi in azione...",
    "🔍 Ricerca intelligente in corso...",
    "💡 Processando informazioni...",
    "🚀 CatchUp al lavoro per te...",
    "📊 Elaborando dati in tempo reale...",
    "🌈 Esplorando possibilità infinite...",
    "🚀 Turbo IA attivata...",
  ];

  // Random AI message state
  // const [currentAiMessage, setCurrentAiMessage] = useState(
  //   aiActivityMessages[Math.floor(Math.random() * aiActivityMessages.length)]
  // );
  // Action Animation Component
  const ActionAnimation = () => (
    <motion.div
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.8 }}
      transition={{ duration: 0.3 }}
      className="flex flex-col items-center justify-center space-y-4 py-8"
    >
      <div className="relative">
        <motion.div
          animate={{
            scale: [1, 1.2, 1],
            rotate: [0, 180, 360],
          }}
          transition={{
            duration: 1.5,
            repeat: Infinity,
            ease: "easeInOut",
          }}
          className="w-12 h-12 rounded-full bg-gradient-to-r from-yellow-400/20 to-orange-500/20 border-4 border-yellow-400/60 flex items-center justify-center"
        >
          <Zap className="w-6 h-6 text-yellow-400" />
        </motion.div>
      </div>
      <div className="text-center space-y-2">
        <motion.p
          animate={{ opacity: [0.6, 1, 0.6] }}
          transition={{ duration: 1.5, repeat: Infinity }}
          className="text-white/90 font-medium"
        >
          Azione in corso...
        </motion.p>
      </div>
      <motion.div
        className="flex space-x-1"
        animate={{ opacity: [0.4, 1, 0.4] }}
        transition={{ duration: 1.2, repeat: Infinity }}
      >
        {[1, 2, 3].map((dot, index) => (
          <motion.div
            key={dot}
            className="w-2 h-2 bg-yellow-400/60 rounded-full"
            animate={{ scale: [1, 1.4, 1] }}
            transition={{
              duration: 0.6,
              repeat: Infinity,
              delay: index * 0.2,
            }}
          />
        ))}
      </motion.div>
    </motion.div>
  );
  // Update AI message when searching starts
  useEffect(() => {
    if (isSearching) {
      setCurrentAiMessage(
        aiActivityMessages[
          Math.floor(Math.random() * aiActivityMessages.length)
        ]
      );
    }
  }, [isSearching]);
  useEffect(() => {
    console.log(
      "isSearching:",
      isSearching,
      "currentNotificationType:",
      currentNotificationType
    );
  }, [isSearching, currentNotificationType]);
  // Searching Animation Component
  const SearchingAnimation = () => (
    <motion.div
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.8 }}
      transition={{ duration: 0.3 }}
      className="flex flex-col items-center justify-center space-y-4 py-8"
    >
      <div className="relative">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
          className="w-12 h-12 rounded-full border-4 border-white/20 border-t-white/80"
        />
        <Search className="absolute inset-0 m-auto w-6 h-6 text-white/60" />
      </div>
      <div className="text-center space-y-2">
        <motion.p
          animate={{ opacity: [0.6, 1, 0.6] }}
          transition={{ duration: 1.5, repeat: Infinity }}
          className="text-white/90 font-medium"
        >
          <p>{currentAiMessage}</p>
        </motion.p>
        {searchQuery && (
          <p className="text-white/70 text-sm max-w-48 mx-auto">
            "{searchQuery}"
          </p>
        )}
      </div>
      <motion.div
        className="flex space-x-1"
        animate={{ opacity: [0.4, 1, 0.4] }}
        transition={{ duration: 1.2, repeat: Infinity }}
      >
        {[1, 2, 3].map((dot, index) => (
          <motion.div
            key={dot}
            className="w-2 h-2 bg-white/60 rounded-full"
            animate={{ scale: [1, 1.2, 1] }}
            transition={{
              duration: 0.6,
              repeat: Infinity,
              delay: index * 0.2,
            }}
          />
        ))}
      </motion.div>
    </motion.div>
  );

  // Helper functions for connection quality
  const getQualityColor = (quality: "excellent" | "good" | "unstable") => {
    switch (quality) {
      case "excellent":
        return "border-green-500";
      case "good":
        return "border-blue-500";
      case "unstable":
        return "border-orange-500";
      default:
        return "border-white/30";
    }
  };

  const getQualityText = (quality: "excellent" | "good" | "unstable") => {
    switch (quality) {
      case "excellent":
        return "Connessione eccellente";
      case "good":
        return "Connessione buona";
      case "unstable":
        return "Connessione instabile";
      default:
        return "Connessione in corso...";
    }
  };

  const getQualityPulseDuration = (
    quality: "excellent" | "good" | "unstable"
  ) => {
    switch (quality) {
      case "excellent":
        return 2.0; // Slow pulse for excellent
      case "good":
        return 1.5; // Medium pulse for good
      case "unstable":
        return 0.8; // Fast pulse for unstable
      default:
        return 2.0;
    }
  };

  // Audio Visualizer Component
  const AudioVisualizer = () => (
    <div className="flex items-center justify-center space-x-1 h-8">
      {[...Array(7)].map((_, index) => (
        <motion.div
          key={index}
          className="w-1 bg-white/60 rounded-full"
          animate={{
            height: [
              8 + Math.sin((index + 1) * 0.5) * 4,
              8 + Math.sin((index + 1) * 0.5 + audioLevel * Math.PI) * 16,
              8 + Math.sin((index + 1) * 0.5) * 4,
            ],
          }}
          transition={{
            duration: 0.6,
            repeat: Infinity,
            delay: index * 0.1,
            ease: "easeInOut",
          }}
        />
      ))}
    </div>
  );

  // Determine if we should render in navigation mode
  const isNavigationMode = renderMode === "navigation";
  const shouldShowFullScreen =
    renderMode === "conversation" && !isNavigationMode;

  return (
    <AnimatePresence>
      {isOpen && (
        <div
          className={
            shouldShowFullScreen
              ? "fixed inset-0 z-50 bg-black/80 backdrop-blur-sm"
              : isNavigationMode
              ? "fixed top-4 right-4 z-50"
              : "fixed inset-x-0 bottom-0 z-50 px-4 pb-4"
          }
        >
          <motion.div
            initial={
              shouldShowFullScreen
                ? { opacity: 0, scale: 0.9 }
                : isNavigationMode
                ? { opacity: 0, scale: 0.8, x: 100 }
                : { y: "100%" }
            }
            animate={
              shouldShowFullScreen
                ? { opacity: 1, scale: 1 }
                : isNavigationMode
                ? { opacity: 1, scale: 1, x: 0 }
                : { y: 0 }
            }
            exit={
              shouldShowFullScreen
                ? { opacity: 0, scale: 0.9 }
                : isNavigationMode
                ? { opacity: 0, scale: 0.8, x: 100 }
                : { y: "100%" }
            }
            transition={{ type: "spring", damping: 25, stiffness: 300 }}
            className={
              shouldShowFullScreen
                ? "relative w-full h-full"
                : isNavigationMode
                ? "relative w-80 h-auto"
                : "relative w-full max-w-md mx-auto"
            }
            {...(!shouldShowFullScreen &&
              !isNavigationMode && {
                drag: "y",
                dragConstraints: { top: 0 },
                dragElastic: 0.9,
                onDragEnd: (_, info) => {
                  if (info.offset.y > 100) {
                    handleClose();
                  }
                },
              })}
          >
            <div
              className={`relative overflow-hidden bg-gradient-to-br from-purple-600/95 to-pink-600/95 backdrop-blur-xl shadow-2xl ${
                shouldShowFullScreen
                  ? "rounded-none h-full"
                  : isNavigationMode
                  ? "rounded-2xl"
                  : "rounded-t-3xl"
              }`}
            >
              <div className="absolute top-3 left-1/2 transform -translate-x-1/2 w-12 h-1 bg-white/20 rounded-full" />

              {/* Close Button */}
              {shouldShowFullScreen ? (
                <button
                  onClick={handleClose}
                  className="absolute right-4 top-4 p-2 rounded-full bg-white/10 hover:bg-white/20 transition-colors"
                >
                  <X className="w-5 h-5 text-white" />
                </button>
              ) : null}

              {/* Floating Balloon */}
              <AnimatePresence>
                {showBalloon && (
                  <motion.div
                    initial={{
                      y: "100vh",
                      x: balloonXPosition,
                      opacity: 0,
                      scale: 0.8,
                    }}
                    animate={{
                      y: ["100vh", "50vh", "-20vh"],
                      x: balloonXPosition,
                      opacity: [0, 1, 1, 0],
                      scale: [0.8, 1, 1, 0.9],
                    }}
                    exit={{
                      y: "-20vh",
                      opacity: 0,
                      scale: 0.8,
                    }}
                    transition={{
                      duration: 4,
                      times: [0, 0.2, 0.8, 1],
                      ease: "easeInOut",
                    }}
                    className="absolute z-10 pointer-events-none"
                    style={{ left: 0 }}
                    onAnimationComplete={() => {
                      setShowBalloon(false);
                      setBalloonMessage("");
                      setBalloonType("info");
                    }}
                  >
                    <div className="relative">
                      {/* Balloon */}
                      <div
                        className={`backdrop-blur-sm rounded-2xl px-4 py-3 shadow-lg border ${
                          balloonMessage
                            ? balloonType === "success"
                              ? "bg-green-500/95 border-green-400/30 text-white"
                              : balloonType === "error"
                              ? "bg-red-500/95 border-red-400/30 text-white"
                              : "bg-blue-500/95 border-blue-400/30 text-white"
                            : "bg-white/95 border-white/20"
                        }`}
                      >
                        <p
                          className={`font-medium text-sm whitespace-nowrap ${
                            balloonMessage ? "text-white" : "text-purple-800"
                          }`}
                        >
                          {balloonMessage ||
                            balloonMessages[
                              Math.floor(Math.random() * balloonMessages.length)
                            ]}
                        </p>
                      </div>
                      {/* Balloon tail */}
                      <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-full">
                        <div
                          className={`w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent ${
                            balloonMessage
                              ? balloonType === "success"
                                ? "border-t-green-500/95"
                                : balloonType === "error"
                                ? "border-t-red-500/95"
                                : "border-t-blue-500/95"
                              : "border-t-white/95"
                          }`}
                        ></div>
                      </div>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>

              <div
                className={
                  shouldShowFullScreen
                    ? "flex flex-col h-full px-6 py-16"
                    : isNavigationMode
                    ? "px-4 py-4"
                    : "px-6 pt-12 pb-6"
                }
              >
                {/* Render different content based on mode */}
                {isNavigationMode ? (
                  /* Navigation Mode - Compact Interface */
                  <div className="flex items-center space-x-3">
                    {/* Compact Avatar */}
                    <motion.div
                      className="relative"
                      animate={{
                        scale: conversation.isSpeaking ? [1, 1.05, 1] : 1,
                      }}
                      transition={{ duration: 2, repeat: Infinity }}
                    >
                      <div className="w-12 h-12 rounded-full overflow-hidden bg-gradient-to-br from-purple-500/20 to-pink-500/20 backdrop-blur-xl border-2 border-white/30">
                        {agentData?.image_url ? (
                          <img
                            src={agentData.image_url}
                            alt={agentData.name}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <img
                            src={DEFAULT_ASSISTANT_IMAGE}
                            alt={DEFAULT_ASSISTANT_NAME}
                            className="w-full h-full object-cover"
                          />
                        )}
                      </div>

                      {/* Status Dot */}
                      {conversation.status === "connected" && (
                        <motion.div
                          className={`absolute -top-1 -right-1 w-3 h-3 rounded-full border border-white ${
                            connectionQuality === "excellent"
                              ? "bg-green-500"
                              : connectionQuality === "good"
                              ? "bg-blue-500"
                              : "bg-orange-500"
                          }`}
                          animate={{
                            scale: [1, 1.2, 1],
                            opacity: [0.8, 1, 0.8],
                          }}
                          transition={{
                            duration: 1.5,
                            repeat: Infinity,
                            ease: "easeInOut",
                          }}
                        />
                      )}
                    </motion.div>

                    {/* Compact Info */}
                    <div className="flex-1 min-w-0">
                      <h3 className="text-white font-medium text-sm truncate">
                        {agentData?.name || DEFAULT_ASSISTANT_NAME}
                      </h3>
                      {conversation.status === "connected" && (
                        <div className="flex items-center space-x-2 text-xs text-white/70">
                          <Clock className="w-3 h-3" />
                          <span className="font-mono">
                            {formatTime(conversationDuration)}
                          </span>
                        </div>
                      )}
                      {pendingNavigation && (
                        <p className="text-xs text-white/80 truncate">
                          Navigating to {pendingNavigation.route}...
                        </p>
                      )}
                    </div>

                    {/* Compact Controls */}
                    <div className="flex items-center space-x-2">
                      {/* Switch back to conversation mode */}
                      <button
                        // onClick={() => setRenderModeState('conversation')}
                        onClick={handleMicClick}
                        className="relative w-9 h-9 rounded-full bg-white/10 hover:bg-white/20 transition-colors p-0 leading-none overflow-hidden"
                        aria-label="Toggle voice call"
                      >
                        <span className="absolute inset-0 flex items-center justify-center pointer-events-none">
                          {isStarting ? (
                            <PhoneOutgoing className="w-4 h-4 text-white animate-pulse block -translate-x-[1px] translate-y-[1px]" />
                          ) : conversation.status === "connected" ? (
                            <PhoneCall className="w-4 h-4 text-white animate-pulse block -translate-x-[1px] translate-y-[1px]" />
                          ) : (
                            <Phone className="w-4 h-4 text-white block" />
                          )}
                        </span>
                      </button>

                      {/* Test navigation button (for development) */}
                      {/* {process.env.NODE_ENV === 'development' && (
                        <button
                          onClick={() => handleNavigationCommand({ type: 'navigate', route: '/deals' })}
                          className="p-2 rounded-full bg-white/10 hover:bg-white/20 transition-colors text-xs"
                          title="Test navigation"
                        >
                          🧪
                        </button>
                      )} */}
                    </div>
                  </div>
                ) : (
                  /* Conversation Mode - Full Interface */
                  <>
                    {/* Top Section - Avatar and Name */}
                    <div className="flex flex-col items-center space-y-4 flex-shrink-0">
                      <motion.div
                        className="relative"
                        animate={{
                          scale: conversation.isSpeaking ? [1, 1.05, 1] : 1,
                        }}
                        transition={{ duration: 2, repeat: Infinity }}
                      >
                        {/* Connection Quality Ring Indicator */}
                        {conversation.status === "connected" && (
                          <motion.div
                            className={`absolute inset-0 rounded-full border-4 ${getQualityColor(
                              connectionQuality
                            )}`}
                            animate={{
                              scale: [1, 1.1, 1],
                              opacity: [0.6, 1, 0.6],
                            }}
                            transition={{
                              duration:
                                getQualityPulseDuration(connectionQuality),
                              repeat: Infinity,
                              ease: "easeInOut",
                            }}
                          />
                        )}

                        {/* Avatar Circle */}
                        <div className="w-24 h-24 rounded-full overflow-hidden bg-gradient-to-br transition-all duration-300 ease-in-out from-purple-500/20 to-pink-500/20 backdrop-blur-xl border-2 border-white/30">
                          {agentData?.image_url ? (
                            <img
                              src={agentData.image_url}
                              alt={agentData.name}
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            <img
                              src={DEFAULT_ASSISTANT_IMAGE}
                              alt={DEFAULT_ASSISTANT_NAME}
                              className="w-full h-full object-cover"
                            />
                          )}
                        </div>

                        {/* Status Dot - Top Right Corner */}
                        {conversation.status === "connected" && (
                          <motion.div
                            className={`absolute -top-1 -right-1 w-5 h-5 rounded-full border-2 border-white ${
                              connectionQuality === "excellent"
                                ? "bg-green-500"
                                : connectionQuality === "good"
                                ? "bg-blue-500"
                                : "bg-orange-500"
                            }`}
                            animate={
                              connectionQuality === "unstable"
                                ? {
                                    scale: [1, 1.3, 1],
                                    opacity: [0.8, 1, 0.8],
                                  }
                                : {
                                    scale: [1, 1.1, 1],
                                    opacity: [0.9, 1, 0.9],
                                  }
                            }
                            transition={{
                              duration:
                                connectionQuality === "unstable" ? 0.5 : 1.5,
                              repeat: Infinity,
                              ease: "easeInOut",
                            }}
                          />
                        )}

                        {/* Central circle animation for starting */}
                        {isStarting && (
                          <>
                            {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((i) => (
                              <motion.div
                                key={i}
                                className="absolute inset-0 rounded-full border-2 border-yellow-500"
                                animate={{
                                  scale: [1, 1.5, 1],
                                  opacity: [0.3, 0, 0.3],
                                }}
                                transition={{
                                  duration: 2,
                                  delay: i * 0.3,
                                  repeat: Infinity,
                                }}
                              />
                            ))}
                          </>
                        )}

                        {/* Connected pulse rings (only when no quality ring is active) */}
                        {conversation.status === "connected" &&
                          !connectionQuality && (
                            <>
                              {[1, 2, 3].map((i) => (
                                <motion.div
                                  key={i}
                                  className="absolute inset-0 rounded-full border-2 border-white/30"
                                  animate={{
                                    scale: [1, 1.5, 1],
                                    opacity: [0.3, 0, 0.3],
                                  }}
                                  transition={{
                                    duration: 2,
                                    delay: i * 0.3,
                                    repeat: Infinity,
                                  }}
                                />
                              ))}
                            </>
                          )}
                      </motion.div>

                      <motion.h2
                        className="text-xl font-semibold text-white text-center"
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                      >
                        {agentData?.name || DEFAULT_ASSISTANT_NAME}
                      </motion.h2>

                      {/* Audio Visualizer - Only when connected */}
                      {conversation.status === "connected" && (
                        <motion.div
                          initial={{ opacity: 0, scale: 0.8 }}
                          animate={{ opacity: 1, scale: 1 }}
                          exit={{ opacity: 0, scale: 0.8 }}
                          className="mt-2"
                        >
                          <AudioVisualizer />
                        </motion.div>
                      )}

                      {/* Connection Quality Text Feedback */}
                      {conversation.status === "connected" && (
                        <motion.p
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          className={`text-sm font-medium ${
                            connectionQuality === "excellent"
                              ? "text-green-400"
                              : connectionQuality === "good"
                              ? "text-blue-400"
                              : "text-orange-400"
                          }`}
                        >
                          {getQualityText(connectionQuality)}
                        </motion.p>
                      )}

                      {/* Conversation Timer */}
                      {conversation.status === "connected" && (
                        <motion.div
                          initial={{ opacity: 0, scale: 0.8 }}
                          animate={{ opacity: 1, scale: 1 }}
                          exit={{ opacity: 0, scale: 0.8 }}
                          className="flex items-center space-x-2 px-3 py-1.5 bg-white/10 backdrop-blur-sm rounded-full border border-white/20"
                        >
                          <Clock className="w-3 h-3 text-white/80" />
                          <span className="text-white/90 font-mono text-xs font-medium">
                            {formatTime(conversationDuration)}
                          </span>
                        </motion.div>
                      )}
                    </div>

                    {/* Middle Section - Deals and Content */}
                    {shouldShowFullScreen && (
                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.3 }}
                        className="flex-1 flex items-center justify-center px-4 py-4"
                      >
                        <div className="w-full max-w-md mx-auto">
                          <div className="bg-white/10 backdrop-blur-sm rounded-2xl border border-white/20 p-6 shadow-lg">
                            <div className="text-center space-y-4">
                              {/* <h3 className="text-lg font-medium text-white/90">
                      
                            {boxTitle}
                          </h3> */}
                              <div className="min-h-48 -m-2">
                                <AnimatePresence mode="wait">
                                  {isSearching ||
                                  currentNotificationType === "query" ? (
                                    <SearchingAnimation key="searching" />
                                  ) : currentNotificationType === "action" ? (
                                    <ActionAnimation key="action" />
                                  ) : (
                                    <motion.div
                                      key="deals"
                                      initial={{ opacity: 0 }}
                                      animate={{ opacity: 1 }}
                                      exit={{ opacity: 0 }}
                                      transition={{ duration: 0.3 }}
                                      className="flex space-x-4 overflow-x-auto pb-4 hide-scrollbar"
                                    >
                                      {deals.map((deal) => (
                                        <QuickDealCard
                                          key={deal.id}
                                          id={deal.id}
                                          title={deal.title}
                                          images={deal.images || []}
                                          discountPercentage={
                                            deal.discount_percentage || 0
                                          }
                                          categoryName={""} // We don't have category name in this query
                                          businessName={
                                            deal.businesses?.name || ""
                                          }
                                          discountedPrice={
                                            deal.discounted_price || 0
                                          }
                                          originalPrice={
                                            deal.original_price || 0
                                          }
                                        />
                                      ))}
                                      {bookings.map((booking) => (
                                        <QuickBookingCard
                                          key={booking.id}
                                          id={booking.id}
                                          booking_date={booking.booking_date}
                                          booking_time={booking.booking_time}
                                          status={booking.status}
                                          deal_title={booking.deals?.title}
                                          business_name={
                                            booking.deals?.businesses?.name
                                          }
                                          deal_image={
                                            booking.deals?.images?.[0]
                                          }
                                          discounted_price={
                                            booking.deals?.discounted_price
                                          }
                                        />
                                      ))}
                                      {/* {deals.length === 0 &&
                                    bookings.length === 0 &&
                                    !isSearching &&
                                    currentNotificationType !== "query" &&
                                    currentNotificationType !== "action" && (
                                      <div className="w-full text-center py-8">
                                        <p className="text-white/60 text-sm">
                                          {boxResponse}
                                        </p>
                                      </div>
                                    )} */}
                                    </motion.div>
                                  )}
                                </AnimatePresence>
                              </div>
                            </div>
                          </div>
                        </div>
                      </motion.div>
                    )}

                    {/* Bottom Section - Call Button and Description */}
                    <div className="flex flex-col items-center space-y-4 flex-shrink-0">
                      <motion.button
                        onClick={handleMicClick}
                        disabled={isStarting}
                        className={`relative w-20 h-20 rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-all ${
                          conversation.status === "connected"
                            ? "bg-red-500 hover:bg-red-600"
                            : isStarting
                            ? "bg-gray-500 hover:bg-gray-600"
                            : "bg-gradient-to-r from-purple-500 to-pink-500"
                        }`}
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        {isStarting ? (
                          <PhoneOutgoing className="w-6 h-6 text-white animate-pulse" />
                        ) : conversation.status === "connected" ? (
                          <PhoneCall className="w-6 h-6 text-white animate-pulse" />
                        ) : (
                          <Phone className="w-6 h-6 text-white" />
                        )}

                        {conversation.status === "connected" && (
                          <motion.div
                            className="absolute inset-0 rounded-full border-4 border-white/30"
                            animate={{
                              scale: [1, 1.2, 1],
                              opacity: [0.5, 0, 0.5],
                            }}
                            transition={{
                              duration: 1.5,
                              repeat: Infinity,
                            }}
                          />
                        )}
                      </motion.button>

                      <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        className="text-center space-y-2"
                      >
                        <p className="text-white/90 text-base font-medium">
                          {conversation.status === "connected"
                            ? "Tocca per terminare"
                            : isStarting
                            ? "Chiamando..."
                            : "Tocca per iniziare"}
                        </p>
                        {transcript && (
                          <p className="text-white/70 text-sm max-w-[250px] mx-auto">
                            {transcript}
                          </p>
                        )}
                        {error && (
                          <p className="text-red-300 text-sm max-w-[250px] mx-auto">
                            {error}
                          </p>
                        )}
                      </motion.div>
                    </div>
                  </>
                )}
              </div>
            </div>
          </motion.div>
        </div>
      )}

      <AlertDialog
        key={"login-alert"}
        open={showLoginAlert}
        onOpenChange={(open) => {
          setShowLoginAlert(open);
          if (!open) onClose();
        }}
      >
        <AlertDialogContent className="z-[100]">
          <AlertDialogHeader>
            <AlertDialogTitle>Accesso richiesto</AlertDialogTitle>
            <AlertDialogDescription>
              Devi loggare per accedere alle funzionalità vocali
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogAction
              onClick={() => {
                setShowLoginAlert(false);
                onClose();
              }}
            >
              OK
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </AnimatePresence>
  );
};

export default VoiceDialog;
