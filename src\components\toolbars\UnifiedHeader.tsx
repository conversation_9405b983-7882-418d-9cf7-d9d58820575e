import { useState, useEffect } from "react";
import {
  ArrowLeft,
  Share2,
  Heart,
  Store,
  MessageCircle,
  SlidersHorizontalIcon,
} from "lucide-react";
import { format } from "date-fns";
import { it } from "date-fns/locale";
import { useAuth } from "@/hooks/auth/useAuth";
import { useNotificationCount } from "@/hooks/useNotificationCount";
import { useNavigate } from "react-router-dom";

interface Tab {
  id: string;
  label: string;
  badge?: number;
  icon: React.ComponentType<{ className?: string }>;
}

interface UnifiedHeaderProps {
  // Layout variants
  variant?:
    | "default"
    | "dashboard"
    | "minimal"
    | "business"
    | "deal-details"
    | "booking"
    | "with-tabs";

  // Navigation
  showBackButton?: boolean;
  onBack?: () => void;

  // Content
  title?: string;
  showGreeting?: boolean;
  showDate?: boolean;

  // Business specific
  businessName?: string;
  isBusiness?: boolean;

  // Deal details specific
  isFavorite?: boolean;
  onToggleFavorite?: () => void;
  onShare?: () => void;

  // Search functionality
  showSearch?: boolean;
  onSearchClick?: () => void;
  activeFiltersCount?: number;
  hasActiveFilters?: boolean;

  // Notifications
  showNotifications?: boolean;

  // Tabs (for with-tabs variant)
  tabs?: Tab[];
  activeTab?: string;
  onTabChange?: (tabId: string) => void;

  // Visual
  showShadow?: boolean;
  sticky?: boolean;
  background?: "default" | "transparent" | "blur";
}

const UnifiedHeader = ({
  variant = "default",
  showBackButton = false,
  onBack,
  title,
  showGreeting = false,
  showDate = false,
  businessName,
  isBusiness = false,
  isFavorite = false,
  onToggleFavorite,
  onShare,
  showSearch = false,
  onSearchClick,
  activeFiltersCount = 0,
  hasActiveFilters = false,
  showNotifications = true,
  tabs = [],
  activeTab,
  onTabChange,
  showShadow = true,
  sticky = true,
  background = "default",
}: UnifiedHeaderProps) => {
  const { userDetails, isAuthenticated } = useAuth();
  const { totalCount } = useNotificationCount();
  const navigate = useNavigate();

  const [isScrolled, setIsScrolled] = useState(false);
  const [hideTabBar, setHideTabBar] = useState(false);

  // Scroll handling for with-tabs variant
  useEffect(() => {
    if (variant !== "with-tabs") return;

    let lastScrollY = 0;
    let scrollTimeout: NodeJS.Timeout;
    let isAnimating = false;

    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      const documentHeight = document.documentElement.scrollHeight;
      const windowHeight = window.innerHeight;
      const maxScroll = documentHeight - windowHeight;

      if (maxScroll < 200) {
        setHideTabBar(false);
        setIsScrolled(currentScrollY > 10);
        return;
      }

      if (isAnimating) return;

      clearTimeout(scrollTimeout);

      const scrollDifference = currentScrollY - lastScrollY;
      const scrollDirection = scrollDifference > 0 ? "down" : "up";

      const hideThreshold = 120;
      const showThreshold = 80;
      const minScrollMovement = 25;

      if (Math.abs(scrollDifference) > minScrollMovement) {
        if (scrollDirection === "down" && currentScrollY > hideThreshold) {
          if (!hideTabBar) {
            isAnimating = true;
            setHideTabBar(true);
            setTimeout(() => {
              isAnimating = false;
            }, 300);
          }
        } else if (scrollDirection === "up" && currentScrollY < showThreshold) {
          if (hideTabBar) {
            isAnimating = true;
            setHideTabBar(false);
            setTimeout(() => {
              isAnimating = false;
            }, 300);
          }
        }

        lastScrollY = currentScrollY;
      }

      setIsScrolled(currentScrollY > 10);

      scrollTimeout = setTimeout(() => {
        if (currentScrollY <= 60 && hideTabBar) {
          isAnimating = true;
          setHideTabBar(false);
          setTimeout(() => {
            isAnimating = false;
          }, 300);
        }
      }, 150);
    };

    let ticking = false;
    const throttledHandler = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          handleScroll();
          ticking = false;
        });
        ticking = true;
      }
    };

    window.addEventListener("scroll", throttledHandler, { passive: true });

    return () => {
      window.removeEventListener("scroll", throttledHandler);
      clearTimeout(scrollTimeout);
    };
  }, [hideTabBar, variant]);

  const handleNotificationClick = () => {
    navigate("/conversations?tab=notifications");
  };

  const handleBackClick = () => {
    if (onBack) {
      onBack();
      return;
    }

    // Se non c'è una history valida, usa un fallback
    const idx = (window.history && (window.history.state as any)?.idx) ?? 0;
    const canGoBack = idx > 0 || window.history.length > 1;

    if (canGoBack) {
      navigate(-1);
    } else {
      navigate("/", { replace: true });
    }
  };

  const formatDateInItalian = () => {
    const formatted = format(new Date(), "EEEE, d MMMM", { locale: it });
    const parts = formatted.split(", ");
    const weekday = parts[0].charAt(0).toUpperCase() + parts[0].slice(1);
    const dayAndMonth = parts[1].split(" ");
    const day = dayAndMonth[0];
    const month =
      dayAndMonth[1].charAt(0).toUpperCase() + dayAndMonth[1].slice(1);
    return `${weekday}, ${day} ${month}`;
  };

  const getTimeBasedGreeting = () => {
    const hour = new Date().getHours();
    if (hour >= 5 && hour < 12) {
      return "Buongiorno";
    } else if (hour >= 12 && hour < 18) {
      return "Buon pomeriggio";
    } else if (hour >= 18 && hour < 22) {
      return "Buona sera";
    } else {
      return "Buona notte";
    }
  };

  const firstName = userDetails?.first_name || "Utente";

  // Header base classes
  const baseClasses = `w-full z-50 ${sticky ? "fixed top-0" : "relative"} ${
    background === "blur"
      ? "bg-white/95 backdrop-blur-md"
      : background === "transparent"
      ? "bg-transparent"
      : "bg-white"
  } ${
    showShadow
      ? variant === "with-tabs" && isScrolled
        ? "shadow-lg border-b border-gray-200/50"
        : "shadow-sm"
      : ""
  }`;

  // Content classes based on variant
  const contentClasses =
    variant === "with-tabs"
      ? `transition-all duration-300 ${
          isScrolled ? "shadow-lg border-b border-gray-200/50" : "shadow-sm"
        }`
      : "";

  // Render left section
  const renderLeftSection = () => {
    if (showBackButton) {
      return (
        <button
          onClick={handleBackClick}
          className="p-2 hover:bg-gray-100 rounded-full transition-colors"
        >
          <ArrowLeft className="h-5 w-5 text-gray-700" />
        </button>
      );
    }

    if (showSearch && onSearchClick) {
      return (
        <button
          className={`relative p-3 rounded-2xl transition-all duration-300 group ${
            hasActiveFilters
              ? "text-primary bg-primary/10 hover:bg-primary/15 shadow-md border border-primary/20"
              : "text-muted-foreground hover:text-foreground bg-muted/50 hover:bg-muted hover:shadow-sm border border-transparent hover:border-border/50"
          }`}
          onClick={onSearchClick}
          aria-label="Apri ricerca"
        >
          <SlidersHorizontalIcon className="h-5 w-5 transition-transform duration-200 group-hover:scale-110" />
          {hasActiveFilters && activeFiltersCount > 0 && (
            <span className="absolute -top-1 -right-1 bg-primary text-primary-foreground text-xs rounded-full w-5 h-5 flex items-center justify-center font-semibold shadow-lg animate-pulse border-2 border-background">
              {activeFiltersCount > 9 ? "9+" : activeFiltersCount}
            </span>
          )}
        </button>
      );
    }

    return <div></div>;
  };

  // Render center section
  const renderCenterSection = () => {
    if (variant === "business" && businessName) {
      return (
        <div className="flex items-center gap-2">
          <div className="w-8 h-8 rounded-full bg-brand-primary/10 flex items-center justify-center text-brand-primary">
            {businessName[0].toUpperCase()}
          </div>
          <span className="font-semibold text-gray-800">{businessName}</span>
        </div>
      );
    }

    if (title) {
      return <h1 className="text-xl text-gray-800 font-semibold">{title}</h1>;
    }

    if (showGreeting && isAuthenticated) {
      return (
        <div className="text-center flex-1">
          <div className="text-lg font-semibold text-gray-800">
            {getTimeBasedGreeting()} {firstName}
          </div>
          {showDate && (
            <div className="text-xs text-gray-500 font-medium mt-0.5">
              {formatDateInItalian()}
            </div>
          )}
        </div>
      );
    }

    if (!isAuthenticated) {
      return (
        <div className="text-center flex-1">
          <div className="text-2xl font-bold bg-gradient-to-r from-brand-secondary via-brand-primary to-purple-600 bg-clip-text text-transparent">
            CatchUp
          </div>
          {showDate && (
            <div className="text-xs text-gray-500 font-medium mt-0.5">
              {formatDateInItalian()}
            </div>
          )}
        </div>
      );
    }

    return <div className="flex-1"></div>;
  };

  // Render right section
  const renderRightSection = () => {
    const actions = [];

    if (variant === "deal-details") {
      if (onToggleFavorite) {
        actions.push(
          <button key="favorite" onClick={onToggleFavorite} className="p-2">
            <Heart
              className={`h-5 w-5 ${
                isFavorite ? "text-red-500 fill-current" : "text-gray-700"
              }`}
            />
          </button>
        );
      }
      if (onShare) {
        actions.push(
          <button key="share" onClick={onShare} className="p-2">
            <Share2 className="h-5 w-5 text-gray-700" />
          </button>
        );
      }
    }

    if (isBusiness) {
      actions.push(
        <Store key="business-icon" className="h-6 w-6 text-brand-primary" />
      );
    }

    if (showNotifications && isAuthenticated) {
      actions.push(
        <button
          key="notifications"
          className="text-muted-foreground hover:text-foreground relative p-3 hover:bg-muted rounded-2xl transition-all duration-300 hover:shadow-sm border border-transparent hover:border-border/50 group"
          onClick={handleNotificationClick}
          aria-label="Apri notifiche"
        >
          <MessageCircle className="h-5 w-5 transition-transform duration-200 group-hover:scale-110" />
          {totalCount > 0 && (
            <span className="absolute -top-1 -right-1 bg-primary text-primary-foreground text-xs rounded-full w-5 h-5 flex items-center justify-center font-semibold shadow-lg animate-pulse border-2 border-background">
              {totalCount > 9 ? "9+" : totalCount}
            </span>
          )}
        </button>
      );
    }

    return <div className="flex items-center gap-2">{actions}</div>;
  };

  return (
    <div className={baseClasses}>
      <div className={contentClasses}>
        {/* Main Header */}
        <header
          className={`px-4 py-3 flex justify-between items-center ${
            variant === "with-tabs"
              ? "border-b border-gray-100/60"
              : variant === "deal-details" || variant === "booking"
              ? "border-b"
              : ""
          }`}
        >
          {renderLeftSection()}
          {renderCenterSection()}
          {renderRightSection()}
        </header>

        {/* Tab Bar for with-tabs variant */}
        {variant === "with-tabs" && tabs.length > 0 && (
          <div
            className={`px-4 overflow-x-auto transition-all duration-500 ease-in-out ${
              hideTabBar
                ? "-translate-y-full opacity-0 max-h-0 py-0"
                : "translate-y-0 opacity-100 max-h-20 py-3"
            }`}
          >
            <div className="flex space-x-2 min-w-max">
              {tabs.map((tab, index) => {
                const isActive = activeTab === tab.id;
                const IconComponent = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => onTabChange?.(tab.id)}
                    className={`relative px-4 py-3 rounded-2xl font-medium text-sm transition-all duration-300 whitespace-nowrap flex items-center gap-2 group ${
                      isActive
                        ? "bg-primary text-primary-foreground shadow-lg border border-primary/20 transform scale-105"
                        : "bg-muted/50 text-muted-foreground hover:bg-muted hover:text-foreground hover:shadow-md hover:scale-102 border border-transparent hover:border-border/50"
                    }`}
                    style={{
                      animationDelay: `${index * 50}ms`,
                    }}
                  >
                    <IconComponent
                      className={`h-4 w-4 transition-all duration-300 ${
                        isActive
                          ? "text-primary-foreground"
                          : "text-muted-foreground group-hover:text-foreground group-hover:scale-110"
                      }`}
                    />
                    <span className="relative z-10 transition-colors duration-300">
                      {tab.label}
                    </span>

                    {isActive && (
                      <div className="absolute inset-0 bg-primary/10 rounded-2xl animate-pulse" />
                    )}

                    {tab.badge && tab.badge > 0 && (
                      <span
                        className={`absolute -top-1 -right-1 text-xs rounded-full w-5 h-5 flex items-center justify-center font-bold shadow-lg z-20 border-2 border-background ${
                          isActive
                            ? "bg-primary-foreground text-primary animate-bounce"
                            : "bg-primary text-primary-foreground animate-pulse"
                        }`}
                      >
                        {tab.badge > 9 ? "9+" : tab.badge}
                      </span>
                    )}
                  </button>
                );
              })}
            </div>

            <div className="mt-2 h-0.5 bg-gradient-to-r from-transparent via-primary/30 to-transparent rounded-full" />
          </div>
        )}
      </div>
    </div>
  );
};

export default UnifiedHeader;
