import { Star, Clock3, MapPin } from "lucide-react";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import SearchBar from "@/components/dashboard/SearchBar";
interface SmartHeroProps {
  searchQuery: string;
  onSearchChange: (value: string) => void;
  onVoiceClick: () => void;
  onQuickChip?: (chip: string) => void;
}
const chips = [{
  key: "oggi",
  label: "Oggi",
  icon: Clock3
}, {
  key: "vicino",
  label: "Vicino a me",
  icon: MapPin
}, {
  key: "ristoranti",
  label: "<PERSON><PERSON><PERSON><PERSON>",
  icon: Star
}, {
  key: "benessere",
  label: "Benessere",
  icon: Star
}];
const SmartHero = ({
  searchQuery,
  onSearchChange,
  onVoiceClick,
  onQuickChip
}: SmartHeroProps) => {
  const [value, setValue] = useState(searchQuery ?? "");
  const handleChange = (v: string) => {
    setValue(v);
    onSearchChange?.(v);
  };
  const handleChip = (key: string) => {
    // Mappatura semplice per ricerca testuale rapida
    const map: Record<string, string> = {
      oggi: "oggi",
      vicino: "vicino a me",
      ristoranti: "ristorante",
      benessere: "benessere"
    };
    const q = map[key] || key;
    setValue(q);
    onSearchChange?.(q);
    onQuickChip?.(key);
  };
  return <header className="mb-6">
      <div className="space-y-2">
        <h1 className="text-2xl font-semibold text-foreground">Offerte vicino a te</h1>
        <p className="text-sm text-muted-foreground">Parla o cerca per trovare offerte perfette in pochi secondi.</p>
      </div>

      <div className="mt-4">
        <SearchBar value={value} onChange={handleChange} placeholder="Cerca offerte e attività" onVoiceClick={onVoiceClick} />
      </div>

      <nav className="mt-3 overflow-x-auto no-scrollbar">
        <ul className="flex gap-2 min-w-max">
          {chips.map(({
          key,
          label,
          icon: Icon
        }) => <li key={key}>
              <Button variant="outline" size="sm" className="rounded-full" onClick={() => handleChip(key)}>
                <Icon className="h-4 w-4 mr-1" /> {label}
              </Button>
            </li>)}
        </ul>
      </nav>
    </header>;
};
export default SmartHero;